<!-- 
  EXAMPLE: How to apply the navigation cache pattern to Users component
  This shows the same pattern used for Partners applied to Users
-->

<template>
  <div class="space-y-6">
    <!-- Users List -->
    <DataTable
      :data="users"
      :columns="columns"
      :loading="loading"
      :total-records="totalRecords"
      :current-page="currentPage"
      :page-size="pageSize"
      :search-query="searchQuery"
      :sort-field="sortField"
      :sort-direction="sortDirection"
      @page-change="handlePageChange"
      @limit-change="handleLimitChange"
      @search="handleSearch"
      @sort="handleSort"
    >
      <!-- Actions -->
      <template #actions="{ item, closeDropdown }">
        <button
          @click="viewUser(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
          View User
        </button>
        <button
          v-if="canEditUser"
          @click="editUser(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit User
        </button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { userApi } from '@/services/userApi' // Hypothetical user API
import { useAuthStore } from '@/stores/auth'
import { navigateWithCache } from '@/utils/navigationCache'

// Router and stores
const router = useRouter()
const authStore = useAuthStore()

// Computed permissions
const canEditUser = computed(() => {
  return authStore.hasAnyRole([1, 2]) || authStore.isSuperUser
})

// Reactive data
const loading = ref(false)
const users = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Table columns
const columns = [
  { key: 'user_id', label: 'User ID', sortable: true },
  { key: 'username', label: 'Username', sortable: true },
  { key: 'email_address', label: 'Email', sortable: true },
  { key: 'role_name', label: 'Role', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true }
]

// Navigation methods using the cache utility
const viewUser = (user: any) => {
  // Navigate with cached data - no API call needed in UserDetails.vue
  console.log('Navigating to view user:', user.user_id)
  navigateWithCache(
    router,
    user,
    'user', // Cache type
    'user-details', // Route name
    { id: user.user_id.toString() } // Route params
  )
}

const editUser = (user: any) => {
  // Navigate with cached data in edit mode
  console.log('Navigating to edit user:', user.user_id)
  navigateWithCache(
    router,
    user,
    'user', // Cache type
    'user-details', // Route name
    { id: user.user_id.toString() }, // Route params
    { mode: 'edit' } // Route query
  )
}

// Data loading methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await userApi.getUsers({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      sort_field: sortField.value,
      sort_direction: sortDirection.value
    })

    if (response.status === 200) {
      users.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    }
  } catch (error) {
    console.error('Error loading users:', error)
  } finally {
    loading.value = false
  }
}

// Event handlers
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleLimitChange = (limit: number) => {
  pageSize.value = limit
  currentPage.value = 1
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  loadData()
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>

<!-- 
  CORRESPONDING UserDetails.vue would look like this:

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { userApi, type User } from '@/services/userApi'
import { getNavigationData, storeNavigationData } from '@/utils/navigationCache'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const initialLoading = ref(true)
const user = ref<User | null>(null)

const fetchUser = async () => {
  const userId = route.params.id as string
  if (!userId) {
    router.push({ name: 'users' })
    return
  }

  // First try to get cached data
  const cachedUser = getNavigationData('user', userId)
  
  if (cachedUser) {
    user.value = cachedUser
    console.log('Using cached user data for ID:', userId)
    return
  }

  // If no cached data, fetch from API
  try {
    const response = await userApi.getUser(userId)
    
    if (response.status === 200) {
      user.value = response.message.data
      
      // Store for future use
      storeNavigationData(user.value, 'user')
      console.log('User loaded from API')
    } else {
      user.value = null
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    user.value = null
  }
}

onMounted(async () => {
  await fetchUser()
  initialLoading.value = false
})
</script>
-->
