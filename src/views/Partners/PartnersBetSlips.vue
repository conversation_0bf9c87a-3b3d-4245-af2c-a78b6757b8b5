<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Partners Bet Slips</h1>
          <p class="mt-1 text-sm text-gray-600">
            View and manage detailed bet slip information from all partners
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="refreshData" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="partner-filter" class="block text-sm font-medium text-gray-700 mb-2">Partner</label>
          <select id="partner-filter" v-model="filters.partner_id" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Partners</option>
            <option v-for="partner in partners" :key="partner.id" :value="partner.id">
              {{ partner.name }}
            </option>
          </select>
        </div>
        <div>
          <label for="slip-status-filter" class="block text-sm font-medium text-gray-700 mb-2">Slip Status</label>
          <select id="slip-status-filter" v-model="filters.slip_status" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="settled">Settled</option>
            <option value="cancelled">Cancelled</option>
            <option value="partially_settled">Partially Settled</option>
          </select>
        </div>
        <div>
          <label for="slip-type-filter" class="block text-sm font-medium text-gray-700 mb-2">Slip Type</label>
          <select id="slip-type-filter" v-model="filters.slip_type" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Types</option>
            <option value="single">Single</option>
            <option value="multiple">Multiple</option>
            <option value="system">System</option>
            <option value="accumulator">Accumulator</option>
          </select>
        </div>
        <div>
          <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
          <select id="date-filter" v-model="filters.date_range" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Partners Bet Slips Table -->
    <DataTable :data="betSlips" :headers="betSlipsHeaders" :loading="loading" :current-page="currentPage"
      :total-records="totalRecords" :page-size="pageSize" title="Partners Bet Slips" row-key="slip_id"
      :has-actions="false" @page-change="handlePageChange" @search="handleSearch" @sort="handleSort"
      @row-click="handleRowClick">
      <!-- Custom Slip Status Cell -->
      <template #cell-slip_status="{ value }">
        <span :class="{
          'bg-yellow-100 text-yellow-800': value === 'pending',
          'bg-green-100 text-green-800': value === 'settled',
          'bg-red-100 text-red-800': value === 'cancelled',
          'bg-blue-100 text-blue-800': value === 'partially_settled'
        }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
          {{ value ?? '-' }}
        </span>
      </template>

      <!-- Custom Slip Type Cell -->
      <template #cell-slip_type="{ value }">
        <span
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {{ value ?? '-' }}
        </span>
      </template>

      <!-- Custom Total Stake Cell -->
      <template #cell-total_stake="{ value }">
        <span class="text-gray-900 font-medium">
          ${{ value?.toFixed(2) ?? '0' }}
        </span>
      </template>

      <!-- Custom Potential Payout Cell -->
      <template #cell-potential_payout="{ value }">
        <span class="text-gray-900 font-medium">
          ${{ value?.toFixed(2) ?? '0' }}
        </span>
      </template>

      <!-- Custom Actual Payout Cell -->
      <template #cell-actual_payout="{ value }">
        <span class="text-gray-900 font-medium">
          {{ value !== null ? `$${value.toLocaleString()}` : '-' }}
        </span>
      </template>

      <!-- Custom Bet Count Cell -->
      <template #cell-bet_count="{ value }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {{ value }} {{ value === 1 ? 'bet' : 'bets' }}
        </span>
      </template>

      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span :class="{
          'bg-green-400 text-green-800': parseInt(value)  === 1,
          'bg-gray-400 text-gray-800': parseInt(value) === 0,
          'bg-red-400 text-red-800': parseInt(value) === 3
        }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
          {{ parseInt(value) === 1 ? 'Won' : parseInt(value) === 0 ? 'Pending' : parseInt(value) === 3 ? 'Lost' : '-' }}
        </span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <div class="flex items-center space-x-2">
          <button @click="viewBetSlip(item)"
            class="text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
            View
          </button>
          <button @click="downloadSlip(item)"
            class="text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200">
            Download
          </button>
          <button v-if="item.slip_status === 'pending'" @click="cancelSlip(item)"
            class="text-red-600 hover:text-red-900 text-sm font-medium transition-colors duration-200">
            Cancel
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { betsApi } from '@/services/betsApi'
import { partnerApi } from '@/services/partnerApi'
import { getNavigationData } from '@/utils/navigationCache'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const betSlips = ref<any[]>([])
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const selectedBet = ref<any>(null)


const betSlipsHeaders = computed(() => ({
  slip_id: "Slip ID",
  // partner_id: "Partner ID",
  partner_name: "Partner Name",
  bet_id: "Bet ID",
  sport_id: "Sport ID",
  parent_match_id: "Parent Match ID",
  parent_market_id: "Parent Market ID",
  market_id: "Market ID",
  selection_id: "Selection ID",
  outcome_name: "Outcome",
  odd_value: "Odd Value",
  pick: "Pick",
  pick_name: "Pick Name",
  winning_outcome: "Winning Outcome",
  ht_scores: "HT Scores",
  ft_scores: "FT Scores",
  et_scores: "ET Scores",
  extra_data: "Extra Data",
  live_bet: "Live Bet",
  status: "Status",
  resulting_type: "Resulting Type",
  start_time: "Start Time",
  // created_at: "Created At",
  // updated_at: "Updated At"
}))


// Get bet_id from route params or query
const betId = computed(() => {
  return route.params.id || route.query.bet_id || route.query.id
})

// Get partner_id from route query
const partnerId = computed(() => {
  return route.query.partner_id || filters.partner_id
})

// Filters
const filters = reactive({
  partner_id: '',
  slip_status: '',
  slip_type: '',
  date_range: ''
})

// Check for cached bet data
const initializeBetData = () => {
  // Try to get cached bet data
  const cachedBet = getNavigationData('bet')
  if (cachedBet) {
    selectedBet.value = cachedBet
    console.log('Using cached bet data XX:', selectedBet.value)

    // Set filters based on cached data
    if (cachedBet.partner_id) {
      filters.partner_id = cachedBet.partner_id.toString()
    }
  } else if (betId.value) {
    console.log('No cached data, using bet_id from route:', betId.value)
    // If no cached data but we have bet_id, we'll fetch it in loadData
  }
}

// Remove sample data - using real API calls

// Methods
const loadData = async () => {
  loading.value = true
  try {
    // Initialize bet data from cache or route
    initializeBetData()

    // Prepare API parameters
    const apiParams: any = {
      page: currentPage.value,
      limit: pageSize.value,
      status: filters.slip_status,
      search: searchQuery.value
    }

    // Add bet_id if available
    if (betId.value) {
      // Use bet_id to filter bet slips for specific bet
      apiParams.bet_id = betId.value.toString()
      console.log('Loading bet slips for bet_id:', betId.value)
    }

    // Add partner_id if available
    if (partnerId.value) {
      apiParams.partner_id = partnerId.value.toString()
      console.log('Loading bet slips for partner_id:', partnerId.value)
    }

    console.log('API Parameters:', JSON.stringify(apiParams))

    // Load bet slips from API
    const response = await partnerApi.getPartnerBetSlips(apiParams.bet_id, apiParams)

    if (response.status === 200) {
      betSlips.value = response.message.data || []
      totalRecords.value = response.message.total || 0
      console.log('Loaded bet slips:', betSlips.value.length)
    } else {
      console.error('Failed to load bet slips:', response)
      betSlips.value = []
      totalRecords.value = 0
    }

    // Load partners for filter dropdown
    const partnersResponse = await partnerApi.getPartners({ limit: 100 })
    if (partnersResponse.status === 200) {
      partners.value = partnersResponse.message.data || []
    }
  } catch (error) {
    console.error('Error loading partners bet slips:', error)
    betSlips.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewBetSlip(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewBetSlip = (slip: any) => {
  console.log('View bet slip:', slip)
  // Implement detailed view logic
}

const downloadSlip = (slip: any) => {
  console.log('Download bet slip:', slip)
  // Implement download logic
}

const cancelSlip = (slip: any) => {
  console.log('Cancel bet slip:', slip)
  // Implement cancel logic
}

const exportData = () => {
  console.log('Export partners bet slips data')
// Implement export logic
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
