<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">System Roles</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage system roles and their permissions
        </p>
      </div>
      <!-- <div class="flex items-center space-x-3">
        <button
          @click="getItems"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
        <router-link
          :to="{ name: 'add-role' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add Role
        </router-link>
      </div> -->
    </div>

    <!-- Search and Filters -->
    <SearchFilter
      v-model:filters="searchFilters"
      search-placeholder="Search roles by name or description..."
      :show-status-filter="true"
      :show-role-filter="false"
      :show-module-filter="false"
      :show-date-filter="false"
      :show-client-filter="false"
      :has-advanced-filters="false"
      @search="handleSearch"
      @filter="handleFilter"
      @clear="handleClearFilters"
    />

    <!-- Roles Data Table -->
    <DataTable
      :data="data"
      :headers="tableHeaders"
      title="System Roles"
      :loading="isLoading"
      :searchable="true"
      :pagination="true"
      :current-page="offset"
      :total-records="total"
      :page-size="limit"
      :has-actions="true"
      empty-message="No roles found"
      @page-change="gotToPage"
      @search="handleSearch"
      @row-click="handleRowClick"
      @limit-change="handleLimitChange"
    >
      <!-- Header Actions -->
      <template #header-actions>
        <button
          @click="getItems"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
        <router-link
          :to="{ name: 'add-role' }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add Role
        </router-link>
      </template>

      <!-- Custom cell templates -->
      <template #cell-index="{ index }">
        <span class="text-gray-900 font-medium">{{ index + 1 }}</span>
      </template>

      <template #cell-name="{ item }">
        <span class="text-gray-900 font-medium">{{ item.name }}</span>
      </template>

      <template #cell-status="{ item }">
        <span
          v-if="parseInt(item.status) === 1"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
        >
          Active
        </span>
        <span
          v-else-if="parseInt(item.status) === 3"
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"
        >
          Deactivated
        </span>
        <span
          v-else
          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800"
        >
          Inactive
        </span>
      </template>

      <template #cell-created_at="{ item }">
        <span class="text-gray-900">{{ formatDate(item.created_at) }}</span>
      </template>

      <!-- Actions -->
      <template #actions="{ item, closeDropdown }">
        <button
          @click="editRole(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit Role
        </button>
      </template>
    </DataTable>
  </div>

</template>

<script>
import { systemApi } from '@/services/systemApi'
import DataTable from '@/components/DataTable.vue'
import SearchFilter from '@/components/SearchFilter.vue'

export default {
  components: {
    DataTable,
    SearchFilter
  },
  data() {
    return {
      isOpen: false,
      isLoading: false,
      items: [],
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      permissions: [],

      // Search filters
      searchFilters: {
        search: '',
        status: '',
      },

      // Table headers for DataTable
      tableHeaders: {
        index: '#',
        name: 'Role Name',
        status: 'Status',
        // created_at: 'Date Created'
      },
    }
  },

  computed: {
    // Use items as data for DataTable
    data() {
      return this.items
    }
  },

  mounted() {
    this.getItems()
  },

  methods: {
    //
    gotToPage(page) {
      let vm = this
      vm.offset = page
      vm.getItems()
    },

    //
    async editRole(role) {
      let app = this
      // Store role data for editing
      app.$router.push({name: 'edit-role', params: {id: role.id}})
    },

    //
    async getItems() {
      let vm = this
      vm.isLoading = true;
      let payload = {page: vm.offset, per_page: vm.limit, timestamp: Date.now(), ...vm.searchFilters}

      try {
        let response = await systemApi.getRoles(payload)
        // console.log("WAJA roles: "+ JSON.stringify(response))
        if (response.status === 200) {
          vm.items = response.message.data || []
          vm.total = vm.items.length
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
        vm.items = []
        vm.total = 0
      }

      vm.isLoading = false;
    },

    // Handle search from DataTable
    handleSearch(query) {
      this.searchFilters.search = query
      this.applyFilters()
    },

    // Handle filter changes from SearchFilter component
    handleFilter(filters) {
      Object.assign(this.searchFilters, filters)
      this.applyFilters()
    },

    // Handle clear filters
    handleClearFilters() {
      this.searchFilters = {
        search: '',
        status: ''
      }
      this.applyFilters()
    },

    // Apply filters to API call
    applyFilters() {
      // For now, just reload data - you can implement actual filtering here
      this.offset = 1
      this.getItems()
    },

    // Handle row click from DataTable
    handleRowClick(item, index) {
      console.log('Row clicked:', item, index)
    },

    // Handle limit change from DataTable
    handleLimitChange(newLimit) {
      this.limit = newLimit
      this.offset = 1 // Reset to first page
      this.getItems() // Reload data with new limit
    },

    //
    getRoleBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-amber-400'
        case 4:
          return 'bg-teal-600'
        case 5:
          return 'bg-sky-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    getStatusBg(id) {
      id = parseInt(id)
      switch (id) {
        case 1:
          return 'bg-green-600'
        case 2:
          return 'bg-orange-600'
        case 3:
          return 'bg-red-600'
        default:
          return 'bg-purple-600'
      }
    },

    //
    async permissionList(list) {
      this.permissions = list
      this.isOpen = true
    },

    //
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        return date.toLocaleString();
      } catch (error) {
        return 'N/A';
      }
    }
  }
}
</script>
