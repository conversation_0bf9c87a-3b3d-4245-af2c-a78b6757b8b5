<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full sm:p-6">
        <!-- Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            Partner Services Details
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div v-if="services && services.length > 0" class="mt-6">
          <!-- Services Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div 
              v-for="service in services" 
              :key="service.id"
              class="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors"
            >
              <!-- Service Header -->
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold text-gray-900">{{ service.service_name || 'Unknown Service' }}</h4>
                    <p class="text-sm text-gray-500">Service ID: {{ service.id }}</p>
                  </div>
                </div>
                <span
                  :class="{
                    'bg-green-100 text-green-800': service.status === 'active' || service.status === 1,
                    'bg-red-100 text-red-800': service.status === 'inactive' || service.status === 0,
                    'bg-yellow-100 text-yellow-800': service.status === 'pending'
                  }"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ getStatusText(service.status) }}
                </span>
              </div>

              <!-- Service Details -->
              <div class="space-y-4">
                <!-- Basic Info -->
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <dt class="text-xs font-medium text-gray-500 mb-1">Partner ID</dt>
                    <dd class="text-sm text-gray-900">{{ service.partner_id || '-' }}</dd>
                  </div>
                  <div class="bg-gray-50 p-3 rounded-lg">
                    <dt class="text-xs font-medium text-gray-500 mb-1">Service Count</dt>
                    <dd class="text-sm text-gray-900">{{ service.service_count || '0' }}</dd>
                  </div>
                </div>

                <!-- Performance Metrics -->
                <div class="bg-gray-50 p-3 rounded-lg">
                  <dt class="text-xs font-medium text-gray-500 mb-1">Rate Limit</dt>
                  <dd class="text-sm text-gray-900 font-medium">{{ service.rate_limit_per_minute || '0' }}/minute</dd>
                  <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-blue-600 h-2 rounded-full" 
                      :style="{ width: getRateLimitPercentage(service.rate_limit_per_minute) + '%' }"
                    ></div>
                  </div>
                </div>

                <!-- Partner Info -->
                <div class="bg-gray-50 p-3 rounded-lg">
                  <dt class="text-xs font-medium text-gray-500 mb-1">Partner Name</dt>
                  <dd class="text-sm text-gray-900">{{ service.partner_name || '-' }}</dd>
                </div>

                <!-- Timestamps -->
                <div class="bg-gray-50 p-3 rounded-lg">
                  <dt class="text-xs font-medium text-gray-500 mb-1">Created At</dt>
                  <dd class="text-sm text-gray-900">{{ formatDate(service.created_at) }}</dd>
                </div>

                <!-- Additional Details -->
                <div v-if="service.description" class="bg-gray-50 p-3 rounded-lg">
                  <dt class="text-xs font-medium text-gray-500 mb-1">Description</dt>
                  <dd class="text-sm text-gray-900">{{ service.description }}</dd>
                </div>
              </div>

              <!-- Service Actions -->
              <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex justify-between items-center">
                  <div class="text-xs text-gray-500">
                    Last updated: {{ formatDate(service.updated_at || service.created_at) }}
                  </div>
                  <button
                    @click="viewServiceDetails(service)"
                    class="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Summary Stats -->
          <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h4 class="text-md font-semibold text-gray-900 mb-4">Services Summary</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ services.length }}</div>
                <div class="text-sm text-gray-500">Total Services</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ activeServicesCount }}</div>
                <div class="text-sm text-gray-500">Active Services</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-red-600">{{ inactiveServicesCount }}</div>
                <div class="text-sm text-gray-500">Inactive Services</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-600">{{ averageRateLimit }}</div>
                <div class="text-sm text-gray-500">Avg Rate Limit</div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="mt-6 text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No services found</h3>
          <p class="mt-1 text-sm text-gray-500">This partner doesn't have any services configured.</p>
        </div>

        <!-- Footer -->
        <div class="mt-6 flex justify-end">
          <button
            @click="$emit('close')"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { formatDate } from '@/utils/formatters'

// Props
interface Props {
  isOpen: boolean
  services: any[]
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  services: () => []
})

// Emits
const emit = defineEmits<{
  close: []
  viewService: [service: any]
}>()

// Computed
const activeServicesCount = computed(() => {
  return props.services.filter(service => 
    service.status === 'active' || service.status === 1
  ).length
})

const inactiveServicesCount = computed(() => {
  return props.services.filter(service => 
    service.status === 'inactive' || service.status === 0
  ).length
})

const averageRateLimit = computed(() => {
  if (props.services.length === 0) return 0
  const total = props.services.reduce((sum, service) => 
    sum + (parseInt(service.rate_limit_per_minute) || 0), 0
  )
  return Math.round(total / props.services.length)
})

// Methods
const getStatusText = (status: any) => {
  if (status === 'active' || status === 1) return 'Active'
  if (status === 'inactive' || status === 0) return 'Inactive'
  if (status === 'pending') return 'Pending'
  return 'Unknown'
}

const getRateLimitPercentage = (rateLimit: any) => {
  const limit = parseInt(rateLimit) || 0
  const maxLimit = 1000 // Assuming max rate limit for visualization
  return Math.min((limit / maxLimit) * 100, 100)
}

const viewServiceDetails = (service: any) => {
  emit('viewService', service)
}
</script>
