<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
        <!-- Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            Partner Settings Details
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div v-if="settings" class="mt-6">
          <!-- Basic Information -->
          <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Partner ID</dt>
                <dd class="text-sm text-gray-900">{{ settings.partner_id || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Partner Name</dt>
                <dd class="text-sm text-gray-900">{{ settings.partner_name || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Setting ID</dt>
                <dd class="text-sm text-gray-900">{{ settings.setting_id || '-' }}</dd>
              </div>
            </div>
          </div>

          <!-- API Configuration -->
          <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-900 mb-4">API Configuration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">API Key</dt>
                <dd class="text-sm text-gray-900 font-mono break-all">{{ maskApiKey(settings.api_key) }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">IP Address</dt>
                <dd class="text-sm text-gray-900">{{ settings.ip_address || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Callback URL</dt>
                <dd class="text-sm text-gray-900 break-all">{{ settings.callback_url || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Rate Limit</dt>
                <dd class="text-sm text-gray-900">{{ settings.rate_limit || '-' }}/min</dd>
              </div>
            </div>
          </div>

          <!-- Financial Settings -->
          <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-900 mb-4">Financial Settings</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Currency</dt>
                <dd class="text-sm text-gray-900">{{ settings.currency || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Denomination</dt>
                <dd class="text-sm text-gray-900">{{ settings.denomination || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Billing Mode</dt>
                <dd class="text-sm text-gray-900 capitalize">{{ settings.billing_mode || '-' }}</dd>
              </div>
            </div>
          </div>

          <!-- System Settings -->
          <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-900 mb-4">System Settings</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Timezone</dt>
                <dd class="text-sm text-gray-900">{{ settings.timezone || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Version</dt>
                <dd class="text-sm text-gray-900">{{ settings.version || '-' }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Transaction Count</dt>
                <dd class="text-sm text-gray-900">{{ settings.trx_count || '0' }}</dd>
              </div>
            </div>
          </div>

          <!-- Websites -->
          <div class="mb-8" v-if="settings.websites">
            <h4 class="text-md font-semibold text-gray-900 mb-4">Authorized Websites</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div v-if="websitesList.length > 0" class="space-y-2">
                <div v-for="(website, index) in websitesList" :key="index" class="flex items-center">
                  <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <span class="text-sm text-gray-900">{{ website }}</span>
                </div>
              </div>
              <div v-else class="text-sm text-gray-500">No websites configured</div>
            </div>
          </div>

          <!-- Timestamps -->
          <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-900 mb-4">Timestamps</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Created At</dt>
                <dd class="text-sm text-gray-900">{{ formatDate(settings.created_at) }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Updated At</dt>
                <dd class="text-sm text-gray-900">{{ formatDate(settings.updated_at) }}</dd>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <dt class="text-sm font-medium text-gray-500 mb-1">Updated By</dt>
                <dd class="text-sm text-gray-900">{{ settings.updated_by || 'System' }}</dd>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="mt-6 text-center py-8">
          <p class="text-gray-500">No settings data available</p>
        </div>

        <!-- Footer -->
        <div class="mt-6 flex justify-end">
          <button
            @click="$emit('close')"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { formatDate } from '@/utils/formatters'

// Props
interface Props {
  isOpen: boolean
  settings: any
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  settings: null
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// Computed
const websitesList = computed(() => {
  if (!props.settings?.websites) return []
  
  try {
    if (typeof props.settings.websites === 'string') {
      return JSON.parse(props.settings.websites)
    }
    return Array.isArray(props.settings.websites) ? props.settings.websites : []
  } catch {
    return []
  }
})

// Methods
const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '-'
  if (apiKey.length <= 8) return apiKey
  return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
}
</script>
